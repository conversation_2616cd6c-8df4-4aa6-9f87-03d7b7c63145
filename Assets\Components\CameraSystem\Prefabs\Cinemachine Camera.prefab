%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2964786134272030383
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4943291896600663887}
  - component: {fileID: 4306420041371873393}
  - component: {fileID: 3496884685508645889}
  - component: {fileID: 7868352802259653049}
  - component: {fileID: 3623505694196872418}
  - component: {fileID: 5937011460426107174}
  m_Layer: 0
  m_Name: FreeLook Camera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4943291896600663887
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2964786134272030383}
  serializedVersion: 2
  m_LocalRotation: {x: 0.20549694, y: 0.008540278, z: -0.0017933444, w: 0.9786189}
  m_LocalPosition: {x: -0.07625121, y: 1.9195442, z: -4.3684287}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5275898103319004444}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4306420041371873393
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2964786134272030383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9dfa5b682dcd46bda6128250e975f58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Priority:
    Enabled: 1
    m_Value: 10
  OutputChannel: 1
  StandbyUpdate: 2
  m_StreamingVersion: 20241001
  m_LegacyPriority: 0
  Target:
    TrackingTarget: {fileID: 0}
    LookAtTarget: {fileID: 0}
    CustomLookAtTarget: 0
  Lens:
    FieldOfView: 40
    OrthographicSize: 10
    NearClipPlane: 0.1
    FarClipPlane: 5000
    Dutch: 0
    ModeOverride: 0
    PhysicalProperties:
      GateFit: 2
      SensorSize: {x: 1, y: 1}
      LensShift: {x: 0, y: 0}
      FocusDistance: 10
      Iso: 200
      ShutterSpeed: 0.005
      Aperture: 16
      BladeCount: 5
      Curvature: {x: 2, y: 11}
      BarrelClipping: 0.25
      Anamorphism: 0
  BlendHint: 0
--- !u!114 &3496884685508645889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2964786134272030383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3b5d7c088409d9a40b7b09aa707777f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetOffset: {x: 0, y: 0, z: 0}
  TrackerSettings:
    BindingMode: 1
    PositionDamping: {x: 1, y: 1, z: 1}
    AngularDampingMode: 0
    RotationDamping: {x: 0, y: 0, z: 0}
    QuaternionDamping: 0
  OrbitStyle: 1
  Radius: 4.3676124
  Orbits:
    Top:
      Radius: 0.01
      Height: 5
    Center:
      Radius: 5
      Height: 0
    Bottom:
      Radius: 0.01
      Height: -5
    SplineCurvature: 1
  RecenteringTarget: 2
  HorizontalAxis:
    Value: 56.749466
    Center: 0
    Range: {x: -180, y: 180}
    Wrap: 1
    Recentering:
      Enabled: 0
      Wait: 1
      Time: 2
    Restrictions: 0
  VerticalAxis:
    Value: 0.32842717
    Center: 0.5
    Range: {x: 0, y: 1}
    Wrap: 0
    Recentering:
      Enabled: 0
      Wait: 1
      Time: 2
    Restrictions: 0
  RadialAxis:
    Value: 1
    Center: 1
    Range: {x: 1, y: 1}
    Wrap: 0
    Recentering:
      Enabled: 0
      Wait: 1
      Time: 2
    Restrictions: 0
--- !u!114 &7868352802259653049
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2964786134272030383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f38bda98361e1de48a4ca2bd86ea3c17, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Composition:
    ScreenPosition: {x: 0, y: 0}
    DeadZone:
      Enabled: 1
      Size: {x: 0, y: 0}
    HardLimits:
      Enabled: 1
      Size: {x: 0.8, y: 0.8}
      Offset: {x: 0, y: 0}
  CenterOnActivate: 1
  TargetOffset: {x: 0, y: 0, z: 0}
  Damping: {x: 0.5, y: 0.5}
  Lookahead:
    Enabled: 0
    Time: 0
    Smoothing: 0
    IgnoreY: 0
--- !u!114 &3623505694196872418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2964786134272030383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a076c17fe76165e4f8ed21498b877bf9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Modifiers:
  - rid: 5581709427942883601
  references:
    version: 2
    RefIds:
    - rid: 5581709427942883601
      type: {class: CinemachineFreeLookModifier/CompositionModifier, ns: Unity.Cinemachine, asm: Unity.Cinemachine}
      data:
        Composition:
          Top:
            ScreenPosition: {x: 0, y: 0}
            DeadZone:
              Enabled: 1
              Size: {x: 0, y: 0}
            HardLimits:
              Enabled: 1
              Size: {x: 0.8, y: 0.8}
              Offset: {x: 0, y: 0}
          Bottom:
            ScreenPosition: {x: 0, y: 0.100000024}
            DeadZone:
              Enabled: 1
              Size: {x: 0, y: 0}
            HardLimits:
              Enabled: 1
              Size: {x: 0.8, y: 0.8}
              Offset: {x: 0, y: 0}
--- !u!114 &5937011460426107174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2964786134272030383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 89875cdc57c54474a8a74efd9b2a3b5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ScanRecursively: 1
  SuppressInputWhileBlending: 1
  IgnoreTimeScale: 0
  m_ControllerManager:
    Controllers:
    - Name: Look Orbit X
      Owner: {fileID: 3496884685508645889}
      Enabled: 0
      Input:
        LegacyInput: Mouse X
        LegacyGain: 100
        CancelDeltaTime: 0
      InputValue: 0
      Driver:
        AccelTime: 0.2
        DecelTime: 0.2
    - Name: Look Orbit Y
      Owner: {fileID: 3496884685508645889}
      Enabled: 0
      Input:
        LegacyInput: Mouse Y
        LegacyGain: -0.5
        CancelDeltaTime: 0
      InputValue: -0
      Driver:
        AccelTime: 0.2
        DecelTime: 0.2
    - Name: Orbit Scale
      Owner: {fileID: 3496884685508645889}
      Enabled: 0
      Input:
        LegacyInput: Mouse ScrollWheel
        LegacyGain: -100
        CancelDeltaTime: 0
      InputValue: -0
      Driver:
        AccelTime: 0
        DecelTime: 0
--- !u!1 &3579338066654108091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5275898103319004444}
  m_Layer: 0
  m_Name: Cinemachine Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5275898103319004444
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3579338066654108091}
  serializedVersion: 2
  m_LocalRotation: {x: 0.94589007, y: 0.039310392, z: -0.008254656, w: 0.32199138}
  m_LocalPosition: {x: -0.26, y: 10.799999, z: -0.14000002}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8593296490904990906}
  - {fileID: 4943291896600663887}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 23.725, y: 0, z: 0}
--- !u!1 &7950648670117636472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8593296490904990906}
  - component: {fileID: 5444076200123223701}
  - component: {fileID: 4402048085671407893}
  - component: {fileID: 5215526542618733697}
  - component: {fileID: 977740176201772033}
  m_Layer: 0
  m_Name: Target Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8593296490904990906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7950648670117636472}
  serializedVersion: 2
  m_LocalRotation: {x: -0.9458901, y: -0.039310396, z: 0.008254657, w: 0.3219914}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5275898103319004444}
  m_LocalEulerAnglesHint: {x: 23.725, y: 0, z: 0}
--- !u!20 &5444076200123223701
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7950648670117636472}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 5000
  field of view: 23
  orthographic: 0
  orthographic size: 10
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!81 &4402048085671407893
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7950648670117636472}
  m_Enabled: 1
--- !u!114 &5215526542618733697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7950648670117636472}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!114 &977740176201772033
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7950648670117636472}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72ece51f2901e7445ab60da3685d6b5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ShowDebugText: 0
  ShowCameraFrustum: 1
  IgnoreTimeScale: 0
  WorldUpOverride: {fileID: 0}
  ChannelMask: -1
  UpdateMethod: 2
  BlendUpdateMethod: 1
  LensModeOverride:
    Enabled: 0
    DefaultMode: 2
  DefaultBlend:
    Style: 1
    Time: 2
    CustomCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  CustomBlends: {fileID: 0}
