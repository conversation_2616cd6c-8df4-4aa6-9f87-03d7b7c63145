using System.Collections;
using CoreTech.CoroutineUtilities;
using Unity.Cinemachine; // Updated namespace
using UnityEngine;

namespace CameraSystem.ManualCamera
{
    public class CameraMover
    {
        private CinemachineCamera camera; // Updated to use CinemachineCamera

        private float minZoom = 2f;
        private float maxZoom = 50f;
        private float zoom = 5f;
        private float smoothSwitchTime = 1.5f;

        private Transform proxyFollow;
        private Coroutine currentCoroutine;
        private CinemachineInputAxisController inputAxisController;

        public CameraMover(CinemachineCamera camera)
        {
            this.camera = camera;

            var orbitalFollow = camera.GetComponent<CinemachineOrbitalFollow>();
            if (orbitalFollow == null)
            {
                Debug.LogError("Cinemachine Orbital Follow is null");
                return;
            }


            inputAxisController = camera.GetComponent<CinemachineInputAxisController>();

            if (inputAxisController == null)
            {
                Debug.LogError("Cinemachine Input Axis Controller is null");
                return;
            }

            zoom = orbitalFollow.Radius;

            proxyFollow = new GameObject("ProxyFollow").transform;
            proxyFollow.SetParent(camera.transform.parent);
        }

        public void SetCameraTarget(Transform target)
        {
            if (camera.Follow == null)
            {
                // This is the first set so no need to do a smooth switch
                camera.Follow = target;
                camera.LookAt = target;
                return;
            }


            if (currentCoroutine != null)
            {
                CoroutineProvider.StopCoroutine(currentCoroutine);
            }

            currentCoroutine = CoroutineProvider.StartCoroutine(SmoothSwitch(target));
        }

        // /// <summary>
        // /// This is a temporary solution to enable smooth switch to follow target.
        // /// When we refactor the camera system, we should implement a more robust solution.
        // /// </summary>
        private IEnumerator SmoothSwitch(Transform target)
        {
        //     inputAxisController.enabled = false;

        //     proxyFollow.position = camera.Follow.position;

        //     Vector3 startPos = proxyFollow.position;

        //     camera.Follow = proxyFollow;
        //     camera.LookAt = target;

        //     float elapsedTime = 0f;
        //     while (elapsedTime < smoothSwitchTime)
        //     {
        //         float t = elapsedTime / smoothSwitchTime;
        //         proxyFollow.position = Vector3.Slerp(startPos, target.position, t);

        //         elapsedTime += Time.deltaTime;
        //         yield return null;
        //     }

        //     camera.Follow = target;
        //     camera.LookAt = target;
        //     currentCoroutine = null;
        //     inputAxisController.enabled = true;
        return null;
        }

        public void AdjustZoom(float zoomDelta)
        {
            // float previousZoom = zoom;
            // zoom -= zoomDelta;
            // zoom = Mathf.Clamp(zoom, minZoom, maxZoom);

            // Debug.Log($"Manual zoom triggered: delta={zoomDelta}, zoom changed from {previousZoom} to {zoom}");

            // var orbitalFollow = camera.GetComponent<CinemachineOrbitalFollow>();
            // if (orbitalFollow == null)
            // {
            //     Debug.LogError("Cinemachine Orbital Follow is null");
            //     return;
            // }
            // zoom = orbitalFollow.Radius;
        }

        public void HorizontalRotation(float value)
        {
            // var panTilt = camera.GetComponent<CinemachinePanTilt>();
            // if (panTilt != null)
            // {
            //     panTilt.PanAxis.Value += value;
            // }
        }

        public void VerticalRotation(float value)
        {
            // var panTilt = camera.GetComponent<CinemachinePanTilt>();
            // if (panTilt != null)
            // {
            //     panTilt.TiltAxis.Value += value;
            // }
        }
    }
}
