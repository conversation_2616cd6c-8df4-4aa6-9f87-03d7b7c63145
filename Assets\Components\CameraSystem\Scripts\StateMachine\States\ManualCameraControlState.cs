using CameraSystem.ManualCamera.Inputs;
using UnityEngine;


namespace CameraSystem.StateMachine.States
{
    public class ManualCameraControlState : ICameraState, IFollowTarget
    {
        private CameraComponentManager componentManager;
        private IListener manualCameraControls;

        public CameraState State => CameraState.ManualCameraControl;
        public void Init(CameraComponentManager cameraComponentManager)
        {
            componentManager = cameraComponentManager;
        }

        public void OnEnter()
        {
            componentManager.FreeLookCam.gameObject.SetActive(true);
            manualCameraControls ??= SetupInputListening();
            manualCameraControls.StartListening();
        }

        public void OnExit()
        {
            componentManager.FreeLookCam.gameObject.SetActive(false);
            manualCameraControls.StopListening();
        }

        public void SwitchTarget(Transform followTarget, Transform lookAtTarget)
        {
            // Target switching will be handled by CameraMovementComponent
            componentManager.FreeLookCam.Follow = followTarget;
            componentManager.FreeLookCam.LookAt = lookAtTarget;
        }

        public void SetFollowTarget(Transform followTarget)
        {
            // Target switching will be handled by CameraMovementComponent
            componentManager.FreeLookCam.Follow = followTarget;
            componentManager.FreeLookCam.LookAt = followTarget;
        }

        private IListener SetupInputListening()
        {
            if (Input.touchSupported)
            {
                Debug.Log("Using touch controls");
                return new TouchInputMapper(componentManager.gameObject);
            }
            Debug.Log("Using mouse controls");
            return new MouseInputMapper(componentManager.gameObject);
        }
    }
}
