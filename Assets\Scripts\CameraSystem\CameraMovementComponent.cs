using Unity.Cinemachine;
using UnityEngine;

namespace CameraSystem
{
    /// <summary>
    /// Single component that handles all camera rotation and zoom functionality
    /// for Cinemachine cameras with OrbitalFollow component.
    /// </summary>
    public class CameraMovementComponent : MonoBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private CinemachineCamera targetCamera;
        
        [Header("Zoom Settings")]
        [SerializeField] private float minZoom = 2f;
        [SerializeField] private float maxZoom = 50f;
        [SerializeField] private float zoomSensitivity = 1f;
        
        [Header("Rotation Settings")]
        [SerializeField] private float horizontalSensitivity = 100f;
        [SerializeField] private float verticalSensitivity = 50f;
        
        [Header("Input Settings")]
        [SerializeField] private bool enableMouseInput = true;
        [SerializeField] private bool enableTouchInput = true;
        [SerializeField] private bool enableKeyboardInput = true;
        
        private CinemachineOrbitalFollow orbitalFollow;
        private bool isDragging = false;
        private Vector2 lastMousePosition;
        private Vector2 lastTouchPosition;
        
        // Touch input variables
        private bool isPinching = false;
        private float lastPinchDistance = 0f;
        
        private void Start()
        {
            InitializeCamera();
        }
        
        private void InitializeCamera()
        {
            if (targetCamera == null)
            {
                targetCamera = GetComponent<CinemachineCamera>();
            }
            
            if (targetCamera == null)
            {
                Debug.LogError("CameraMovementComponent: No CinemachineCamera found!");
                return;
            }
            
            orbitalFollow = targetCamera.GetComponent<CinemachineOrbitalFollow>();
            if (orbitalFollow == null)
            {
                Debug.LogError("CameraMovementComponent: CinemachineOrbitalFollow component not found on camera!");
                return;
            }
            
            Debug.Log("CameraMovementComponent: Initialized successfully");
        }
        
        private void Update()
        {
            if (orbitalFollow == null) return;
            
            HandleInput();
        }
        
        private void HandleInput()
        {
            // Handle mouse input
            if (enableMouseInput && !Input.touchSupported)
            {
                HandleMouseInput();
            }
            
            // Handle touch input
            if (enableTouchInput && Input.touchSupported)
            {
                HandleTouchInput();
            }
            
            // Handle keyboard input
            if (enableKeyboardInput)
            {
                HandleKeyboardInput();
            }
        }
        
        private void HandleMouseInput()
        {
            // Right click drag for rotation
            if (Input.GetMouseButtonDown(1))
            {
                isDragging = true;
                lastMousePosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(1))
            {
                isDragging = false;
            }
            
            if (isDragging && Input.GetMouseButton(1))
            {
                Vector2 currentMousePosition = Input.mousePosition;
                Vector2 mouseDelta = currentMousePosition - lastMousePosition;
                
                RotateCamera(mouseDelta.x, mouseDelta.y);
                
                lastMousePosition = currentMousePosition;
            }
            
            // Mouse scroll wheel for zoom
            float scrollDelta = Input.GetAxis("Mouse ScrollWheel");
            if (Mathf.Abs(scrollDelta) > 0.01f)
            {
                ZoomCamera(-scrollDelta * zoomSensitivity * 10f);
            }
        }
        
        private void HandleTouchInput()
        {
            if (Input.touchCount == 1)
            {
                // Single finger drag for rotation
                Touch touch = Input.GetTouch(0);
                
                if (touch.phase == TouchPhase.Began)
                {
                    isDragging = true;
                    lastTouchPosition = touch.position;
                }
                else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
                {
                    isDragging = false;
                }
                else if (touch.phase == TouchPhase.Moved && isDragging)
                {
                    Vector2 touchDelta = touch.position - lastTouchPosition;
                    RotateCamera(touchDelta.x, touchDelta.y);
                    lastTouchPosition = touch.position;
                }
            }
            else if (Input.touchCount == 2)
            {
                // Two finger pinch for zoom
                Touch touch1 = Input.GetTouch(0);
                Touch touch2 = Input.GetTouch(1);
                
                float currentPinchDistance = Vector2.Distance(touch1.position, touch2.position);
                
                if (!isPinching)
                {
                    isPinching = true;
                    lastPinchDistance = currentPinchDistance;
                }
                else
                {
                    float pinchDelta = currentPinchDistance - lastPinchDistance;
                    ZoomCamera(-pinchDelta * zoomSensitivity * 0.01f);
                    lastPinchDistance = currentPinchDistance;
                }
            }
            else
            {
                isPinching = false;
                isDragging = false;
            }
        }
        
        private void HandleKeyboardInput()
        {
            // Arrow keys for rotation
            if (Input.GetKey(KeyCode.LeftArrow))
            {
                RotateCamera(-horizontalSensitivity * Time.deltaTime, 0);
            }
            if (Input.GetKey(KeyCode.RightArrow))
            {
                RotateCamera(horizontalSensitivity * Time.deltaTime, 0);
            }
            
            // Up/Down arrows for zoom
            if (Input.GetKey(KeyCode.UpArrow))
            {
                ZoomCamera(-zoomSensitivity * Time.deltaTime);
            }
            if (Input.GetKey(KeyCode.DownArrow))
            {
                ZoomCamera(zoomSensitivity * Time.deltaTime);
            }
        }
        
        /// <summary>
        /// Rotate the camera horizontally and vertically
        /// </summary>
        /// <param name="horizontalDelta">Horizontal rotation delta</param>
        /// <param name="verticalDelta">Vertical rotation delta</param>
        public void RotateCamera(float horizontalDelta, float verticalDelta)
        {
            if (orbitalFollow == null) return;
            
            // Apply horizontal rotation
            float newHorizontalValue = orbitalFollow.HorizontalAxis.Value + (horizontalDelta * horizontalSensitivity * 0.01f);
            orbitalFollow.HorizontalAxis.Value = newHorizontalValue;
            
            // Apply vertical rotation with clamping
            float newVerticalValue = orbitalFollow.VerticalAxis.Value - (verticalDelta * verticalSensitivity * 0.01f);
            newVerticalValue = Mathf.Clamp(newVerticalValue, orbitalFollow.VerticalAxis.Range.x, orbitalFollow.VerticalAxis.Range.y);
            orbitalFollow.VerticalAxis.Value = newVerticalValue;
            
            Debug.Log($"Camera rotated: H={newHorizontalValue:F2}, V={newVerticalValue:F2}");
        }
        
        /// <summary>
        /// Zoom the camera in or out
        /// </summary>
        /// <param name="zoomDelta">Positive values zoom out, negative values zoom in</param>
        public void ZoomCamera(float zoomDelta)
        {
            if (orbitalFollow == null) return;
            
            float newRadius = orbitalFollow.Radius + zoomDelta;
            newRadius = Mathf.Clamp(newRadius, minZoom, maxZoom);
            orbitalFollow.Radius = newRadius;
            
            Debug.Log($"Camera zoomed: Radius={newRadius:F2}");
        }
        
        /// <summary>
        /// Set the camera's zoom level directly
        /// </summary>
        /// <param name="zoomLevel">The desired zoom level (radius)</param>
        public void SetZoom(float zoomLevel)
        {
            if (orbitalFollow == null) return;
            
            float clampedZoom = Mathf.Clamp(zoomLevel, minZoom, maxZoom);
            orbitalFollow.Radius = clampedZoom;
            
            Debug.Log($"Camera zoom set to: {clampedZoom:F2}");
        }
        
        /// <summary>
        /// Set the camera's rotation directly
        /// </summary>
        /// <param name="horizontalAngle">Horizontal angle in degrees</param>
        /// <param name="verticalValue">Vertical value (0-1 range)</param>
        public void SetRotation(float horizontalAngle, float verticalValue)
        {
            if (orbitalFollow == null) return;
            
            orbitalFollow.HorizontalAxis.Value = horizontalAngle;
            orbitalFollow.VerticalAxis.Value = Mathf.Clamp(verticalValue, orbitalFollow.VerticalAxis.Range.x, orbitalFollow.VerticalAxis.Range.y);
            
            Debug.Log($"Camera rotation set to: H={horizontalAngle:F2}, V={verticalValue:F2}");
        }
        
        /// <summary>
        /// Get the current zoom level
        /// </summary>
        public float GetCurrentZoom()
        {
            return orbitalFollow != null ? orbitalFollow.Radius : 0f;
        }
        
        /// <summary>
        /// Get the current rotation values
        /// </summary>
        public Vector2 GetCurrentRotation()
        {
            if (orbitalFollow == null) return Vector2.zero;
            return new Vector2(orbitalFollow.HorizontalAxis.Value, orbitalFollow.VerticalAxis.Value);
        }
    }
}
