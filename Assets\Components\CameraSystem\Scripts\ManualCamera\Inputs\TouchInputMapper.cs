using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class TouchInputMapper : IListener
    {
        private TouchInput touchInput;

        public TouchInputMapper(GameObject cameraComponentManager)
        {
            if (!cameraComponentManager.TryGetComponent<TouchInput>(out touchInput))
            {
                touchInput = cameraComponentManager.AddComponent<TouchInput>();
            }
        }

        public void StartListening()
        {
            touchInput.StartListening();
            touchInput.OnPinchDrag.OnDrag += HandlePinch;
            touchInput.OnOneFingerDrag.OnDrag += HandleOneFingerDrag;
            touchInput.OnOneFingerDrag.OnDragEnd += HandleOneFingerDragEnd;
        }

        public void StopListening()
        {
            touchInput.StopListening();
            touchInput.OnPinchDrag.OnDrag -= HandlePinch;
            touchInput.OnOneFingerDrag.OnDrag -= HandleOneFingerDrag;
            touchInput.OnOneFingerDrag.OnDragEnd -= HandleOneFingerDragEnd;
        }

        private void HandleOneFingerDrag(Vector2 vector)
        {
            // Camera movement will be handled by CameraMovementComponent
        }

        private void HandleOneFingerDragEnd()
        {
            // Camera movement will be handled by CameraMovementComponent
        }

        private void HandlePinch(float value)
        {
            // Camera movement will be handled by CameraMovementComponent
        }
    }
}
