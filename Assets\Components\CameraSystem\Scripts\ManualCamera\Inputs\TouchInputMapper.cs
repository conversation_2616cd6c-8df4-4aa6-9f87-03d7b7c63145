using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class TouchInputMapper : IListener
    {
        private CameraMover cameraMover;
        private float zoomSpeedMultiplier = 0.01f;
        private float horizontalRotationMultiplier = 0.02f;
        private float verticalRotationMultiplier = 0.02f;
        private TouchInput touchInput;

        public TouchInputMapper(GameObject cameraComponentManager, CameraMover cameraMover)
        {
            this.cameraMover = cameraMover;
            if (!cameraComponentManager.TryGetComponent<TouchInput>(out touchInput))
            {
                touchInput = cameraComponentManager.AddComponent<TouchInput>();
            }
        }

        public void StartListening()
        {
            touchInput.StartListening();
            touchInput.OnPinchDrag.OnDrag += HandlePinch;
            touchInput.OnOneFingerDrag.OnDrag += HandleOneFingerDrag;
            touchInput.OnOneFingerDrag.OnDragEnd += HandleOneFingerDragEnd;
        }

        public void StopListening()
        {
            touchInput.StopListening();
            touchInput.OnPinchDrag.OnDrag -= HandlePinch;
            touchInput.OnOneFingerDrag.OnDrag -= HandleOneFingerDrag;
            touchInput.OnOneFingerDrag.OnDragEnd -= HandleOneFingerDragEnd;
        }

        private void HandleOneFingerDrag(Vector2 vector)
        {
            // cameraMover.HorizontalRotation(-vector.x * horizontalRotationMultiplier);
            // cameraMover.VerticalRotation(-vector.y * verticalRotationMultiplier);
        }

        private void HandleOneFingerDragEnd()
        {
            // Reset camera input axis to 0
            // cameraMover.HorizontalRotation(0);
            // cameraMover.VerticalRotation(0);
        }

        private void HandlePinch(float value)
        {
            // Debug.Log($"Pinch-to-zoom triggered: value={value}, adjusted={value * zoomSpeedMultiplier}");
            // cameraMover.AdjustZoom(value * zoomSpeedMultiplier);
        }
    }
}
