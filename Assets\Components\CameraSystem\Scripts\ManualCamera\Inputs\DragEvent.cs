using System;
using System.Collections;
using CoreTech.CoroutineUtilities;

namespace CameraSystem.ManualCamera.Inputs
{
    public class DragEvent<T> : IDragEvent<T>
    {
        public event Action OnDragStart;
        public event Action<T> OnDrag;
        public event Action OnDragEnd;

        private bool isDragging = false;
        private bool wasDraggedLastFrame = false;

        public void Drag(T value)
        {
            OnDrag?.Invoke(value);
            wasDraggedLastFrame = true;
            
            if (!isDragging)
            {
                isDragging = true;
                
                CoroutineProvider.StartCoroutine(DragEnd());
            }
        }

        private IEnumerator DragEnd()
        {
            while(wasDraggedLastFrame)
            {
                wasDraggedLastFrame = false;
                yield return null;
            }
            isDragging = false;
            OnDragEnd?.Invoke();
        }
    }
}
