using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class TouchInput : BaseInput
    {
        public IDragEvent<float> OnPinchDrag = new DragEvent<float>();
        public IDragEvent<Vector2> OnOneFingerDrag = new DragEvent<Vector2>();
        public IDragEvent<Vector2> OnTwoFingerDrag = new DragEvent<Vector2>();
        private List<int> validTouchIds = new List<int>();
        private List<int> invalidTouchIds = new List<int>();

        protected override IEnumerator InputListener()
        {
            while (true)
            {
                UpdateValidTouchIds();
                switch (Input.touchCount)
                {
                    case 0:
                        break;
                    case 1:
                        var touch = Input.GetTouch(0);
                        if (IsTouchValid(touch))
                        {
                            OnOneFingerDrag.Drag(GetAvgFingerMovement(new Touch[] {touch}));
                        }
                        break;
                    case 2:
                        var touch0 = Input.GetTouch(0);
                        var touch1 = Input.GetTouch(1);
                        if (IsTouchValid(touch0) && IsTouchValid(touch1))
                        {
                            OnPinchDrag.Drag(CalculatePinch(touch0, touch1));
                            OnTwoFingerDrag.Drag(GetAvgFingerMovement(new Touch[] {touch0, touch1}));
                        }
                        break;
                    default:
                        Debug.Log("More than 2 inputs, doing nothing");
                        break;
                }
                yield return null;
            }
        }


        private void UpdateValidTouchIds()
        {
            foreach (var touch in Input.touches)
            {
                if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
                {
                    validTouchIds.Remove(touch.fingerId);
                    invalidTouchIds.Remove(touch.fingerId);
                }

                if (touch.phase.Equals(TouchPhase.Began))
                {
                    if (interactableUiArea == null)
                    {
                        Debug.LogWarning("Ignoring touch as no interactable area found");
                        continue;
                    }
                    else if (interactableUiArea.IsPointerOver)
                    {
                        validTouchIds.Add(touch.fingerId);
                    }
                    else
                    {
                        invalidTouchIds.Add(touch.fingerId);
                    }
                }
            }
        }

        private bool IsTouchValid(Touch touch)
        {
            if (validTouchIds.Contains(touch.fingerId) && 
                !invalidTouchIds.Contains(touch.fingerId) &&
                interactableUiArea.IsPointerOver)
            {
                return true;
            }
            return false;
        }

        private Vector2 GetAvgFingerMovement(Touch[] touches)
        {
            Vector2 totalDist = Vector2.zero;
            foreach (var touch in touches)
            {
                totalDist += touch.deltaPosition;
            }
            return totalDist / touches.Length;
        }

        private float CalculatePinch(Touch touch0, Touch touch1)
        {
            var previousPos0 = touch0.position - touch0.deltaPosition;
            var previousPos1 = touch1.position - touch1.deltaPosition;

            var previousMag = (previousPos0 - previousPos1).magnitude;
            var currentMag = (touch0.position - touch1.position).magnitude;

            return currentMag - previousMag;
        }
    }
}
