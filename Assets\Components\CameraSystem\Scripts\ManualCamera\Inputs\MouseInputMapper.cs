using UnityEngine;

namespace CameraSystem.ManualCamera.Inputs
{
    public class MouseInputMapper : IListener
    {
        private MouseInput mouseInput;

        public MouseInputMapper(GameObject cameraComponentManager)
        {
            if (!cameraComponentManager.TryGetComponent<MouseInput>(out mouseInput))
            {
                mouseInput = cameraComponentManager.AddComponent<MouseInput>();
            }
        }

        public void StartListening()
        {
            mouseInput.StartListening();
            // Input handling will be moved to CameraMovementComponent
        }

        public void StopListening()
        {
            mouseInput.StopListening();
        }
    }
}
