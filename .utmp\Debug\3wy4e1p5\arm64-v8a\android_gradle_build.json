{"buildFiles": ["C:\\Users\\<USER>\\Documents\\btv-genex-mdi-unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\btv-genex-mdi-unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\FramePacing\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Program Files\\6000.0.44f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\btv-genex-mdi-unity\\.utmp\\Debug\\3wy4e1p5\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Program Files\\6000.0.44f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\btv-genex-mdi-unity\\.utmp\\Debug\\3wy4e1p5\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"swappywrapper::@7e25bd1f32ce224db4e9": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "swappywrapper", "output": "C:\\Users\\<USER>\\Documents\\btv-genex-mdi-unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\Debug\\3wy4e1p5\\obj\\arm64-v8a\\libswappywrapper.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Program Files\\6000.0.44f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Program Files\\6000.0.44f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}