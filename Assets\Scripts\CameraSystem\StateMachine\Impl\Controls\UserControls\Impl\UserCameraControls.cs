using System;
using Unity.Cinemachine;
using DG.Tweening;
using Framework.Models.Inputs.Impl.Devices;
using Framework.Models.Inputs.Impl.States;

namespace CameraSystem.StateMachine.Impl.Controls.UserControls.Impl
{
    public class UserCameraControls : IUserCameraControls
    {
        private const float CountDownDuration = 1.0f;

        private readonly IDeviceInput deviceInput;

        private Tweener countDownTween;

        public event Action<bool> UserIsInteracting;

        public UserCameraControls(IDeviceInput deviceInput, CinemachineCamera activeCamera)
        {
            this.deviceInput = deviceInput;
            // Camera movement will be handled by CameraMovementComponent
        }

        public void Enable()
        {
            StartCountDown();

            deviceInput.ButtonPress += OnButtonPress;
            deviceInput.ButtonHold += OnButtonHold;
            deviceInput.ButtonRelease += OnButtonRelease;
        }

        public void Disable()
        {
            ResetTween();

            deviceInput.ButtonPress -= OnButtonPress;
            deviceInput.ButtonHold -= OnButtonHold;
            deviceInput.ButtonRelease -= OnButtonRelease;
        }

        private void OnButtonPress(InputButton button)
        {
            if (button == InputButton.UpArrow)
            {
                // Camera zoom will be handled by CameraMovementComponent
            }
            else if (button == InputButton.DownArrow)
            {
                // Camera zoom will be handled by CameraMovementComponent
            }

            UserIsInteracting?.Invoke(true);
        }

        private void OnButtonHold(InputButton button)
        {
            if (button == InputButton.LeftArrow)
            {
                // Camera rotation will be handled by CameraMovementComponent
            }
            else if (button == InputButton.RightArrow)
            {
                // Camera rotation will be handled by CameraMovementComponent
            }
        }

        private void OnButtonRelease(InputButton button)
        {
            StartCountDown();

            UserIsInteracting?.Invoke(false);
        }

        private void StartCountDown()
        {
            countDownTween = DOVirtual.Float(CountDownDuration, 0.0f, CountDownDuration, value => { })
                .OnComplete(UserStoppedInteracting);
        }

        private void UserStoppedInteracting()
        {
            ResetTween();
        }

        private void ResetTween()
        {
            countDownTween.Pause();
            countDownTween.Kill();
            countDownTween = null;
        }
    }
}