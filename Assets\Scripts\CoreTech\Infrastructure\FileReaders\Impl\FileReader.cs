﻿using System;
using System.Collections;
using System.IO;
using CoreTech.CoroutineUtilities;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using UnityEngine.Networking;

namespace CoreTech.Infrastructure.FileReaders.Impl
{
    public class FileReader : IFileReader
    {
        public IPromise<string> ReadFile(string path,string fileName)
        {
            var outcome = new Promise<string>();
            
            var filePath = GetCombinedPath(path, fileName);
            
            CoroutineProvider.StartCoroutine(Load(filePath, outcome, downloadHandler => { outcome.Dispatch(downloadHandler.text); }));
            
            return outcome;
        }
        
        public IPromise<byte[]> ReadData(string path,string fileName)
        {
            var outcome = new Promise<byte[]>();
            
            var filePath = GetCombinedPath(path, fileName);

            CoroutineProvider.StartCoroutine(Load(filePath, outcome, downloadHandler => { outcome.Dispatch(downloadHandler.data); }));

            return outcome;
        }

        public bool FileExists(string filePath)
        {
            return File.Exists(filePath);
        }

        
        private static IEnumerator Load(string filePath, IBasePromise outcome, Action<DownloadHandler> handlerResult)
        {
            var request = UnityWebRequest.Get(filePath);
            var operation = request.SendWebRequest();

            while (!operation.isDone)
            {
                outcome.ReportProgress(operation.progress);
                yield return null;
            }

            if (request.result == UnityWebRequest.Result.Success)
            {
                handlerResult?.Invoke(request.downloadHandler);
            }
            else
            {
                outcome.ReportFail(new Exception($"Could not read file at {filePath} with error {request.error}"));
            }

            request.Dispose();
        }

        private static string GetCombinedPath(string assetPath, string fileName)
        {
            string filePath;

#if UNITY_WEBGL && !UNITY_EDITOR
    // WebGL: Use Application.streamingAssetsPath (already a proper URL)
    filePath = $"{Application.streamingAssetsPath}/{fileName}";
#else
            // Other platforms: file:// + full path
            filePath = Path.Combine(assetPath, fileName).Replace("\\", "/");
            filePath = "file://" + filePath;
#endif
            
            return filePath;
        }
    }
}