using CameraSystem.StateMachine.Impl.Controls.Impl.Base;
using Unity.Cinemachine;
using DG.Tweening;
using UnityEngine;

namespace CameraSystem.StateMachine.Impl.Controls.UserControls.Impl.Movements.PhysicalZoom
{
    public class CameraZoomHandler : BaseCameraControls, ICameraZoomHandler
    {
        private const int StepsCount = 3;
        private const float Duration = 1.0f;

        private readonly float[] zoomLevels;

        private int currentIndex;
        private float currentDistance;

        public CameraZoomHandler(CinemachineCamera stateCamera) : base(stateCamera)
        {
            currentIndex = 2;
            zoomLevels = new[] { 0.25f, 0.5f, 1f };
        }

        public void MoveCloser()
        {
            if (currentIndex > 0)
            {
                currentIndex--;
                Debug.Log($"Physical zoom triggered: MoveCloser to level {currentIndex} (zoom factor: {zoomLevels[currentIndex]})");
                Zoom();
            }
        }

        public void MoveFurther()
        {
            if (currentIndex < StepsCount - 1)
            {
                currentIndex++;
                Debug.Log($"Physical zoom triggered: MoveFurther to level {currentIndex} (zoom factor: {zoomLevels[currentIndex]})");
                Zoom();
            }
        }

        private void Zoom()
        {
            if (Camera.LookAt == null)
            {
                return;
            }

            var cameraTransform = Camera.transform;
            var lookAtPosition = Camera.LookAt.position;

            if (currentDistance == 0)
            {
                currentDistance = Vector3.Distance(cameraTransform.position, lookAtPosition);
            }

            var newDistance = currentDistance * zoomLevels[currentIndex];
            var direction = cameraTransform.position - lookAtPosition;
            var newPosition = lookAtPosition + direction.normalized * newDistance;

            cameraTransform.DOMove(newPosition, Duration);

            Debug.Log($"Physical zoom applied: radius changed to {newDistance}");
        }
    }
}